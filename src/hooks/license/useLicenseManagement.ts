'use client';
import { useState, useEffect } from 'react';

export interface LicenseApiKey {
  id: string;
  keyHash: string;
  plaintextKey: string | null; // Original API key for display (can be null for legacy keys)
  serverIp: string;
  serverName?: string;
  isActive: boolean;
  lastUsed?: string;
  createdAt: string;
  updatedAt: string;
}

export interface License {
  id: string;
  userId: string;
  spigotUsername?: string;
  licenseType: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  expiresAt?: string;
  apiKeys: LicenseApiKey[];
}

export interface CreateApiKeyRequest {
  licenseId: string;
  serverIp: string;
  serverName?: string;
}

export interface CreateApiKeyResponse {
  apiKey: string; // The actual API key (only returned once)
  keyData: LicenseApiKey;
}

export function useLicenseManagement() {
  const [licenses, setLicenses] = useState<License[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchLicenses = async () => {
    setLoading(true);
    setError(null);
    try {
      const res = await fetch('/api/license/manage');
      const data = await res.json();
      if (res.ok) {
        setLicenses(data.licenses || []);
      } else {
        setError(data.error || 'Failed to fetch licenses');
      }
    } catch (err) {
      setError('Network error');
    }
    setLoading(false);
  };

  const createApiKey = async (request: CreateApiKeyRequest): Promise<CreateApiKeyResponse | null> => {
    try {
      const res = await fetch('/api/license/api-keys', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(request)
      });
      const data = await res.json();
      if (res.ok) {
        await fetchLicenses(); // Refresh licenses
        return data;
      } else {
        throw new Error(data.error || 'Failed to create API key');
      }
    } catch (err: any) {
      setError(err.message);
      return null;
    }
  };

  const revokeApiKey = async (keyId: string): Promise<boolean> => {
    try {
      const res = await fetch(`/api/license/api-keys/${keyId}`, {
        method: 'DELETE'
      });
      const data = await res.json();
      if (res.ok) {
        await fetchLicenses(); // Refresh licenses
        return true;
      } else {
        throw new Error(data.error || 'Failed to revoke API key');
      }
    } catch (err: any) {
      setError(err.message);
      return false;
    }
  };

  const updateApiKey = async (keyId: string, updates: { serverName?: string; isActive?: boolean }): Promise<boolean> => {
    try {
      const res = await fetch(`/api/license/api-keys/${keyId}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(updates)
      });
      const data = await res.json();
      if (res.ok) {
        await fetchLicenses(); // Refresh licenses
        return true;
      } else {
        throw new Error(data.error || 'Failed to update API key');
      }
    } catch (err: any) {
      setError(err.message);
      return false;
    }
  };

  useEffect(() => {
    fetchLicenses();
  }, []);

  return {
    licenses,
    loading,
    error,
    refetch: fetchLicenses,
    createApiKey,
    revokeApiKey,
    updateApiKey,
    clearError: () => setError(null)
  };
}
