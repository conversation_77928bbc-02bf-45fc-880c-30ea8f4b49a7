/**
 * Migration script to handle existing API keys that don't have plaintextKey field
 * This script will mark existing API keys as needing regeneration
 */

const { PrismaClient } = require('@prisma/client');

async function migrateApiKeys() {
  const prisma = new PrismaClient();
  
  try {
    console.log('Starting API key migration...');
    
    // Find all API keys that don't have a plaintextKey
    const apiKeysWithoutPlaintext = await prisma.licenseApiKey.findMany({
      where: {
        OR: [
          { plaintextKey: null },
          { plaintextKey: '' },
          { plaintextKey: { equals: undefined } }
        ]
      },
      include: {
        license: {
          select: {
            id: true,
            licenseType: true,
            user: {
              select: {
                email: true
              }
            }
          }
        }
      }
    });
    
    console.log(`Found ${apiKeysWithoutPlaintext.length} API keys without plaintext keys`);
    
    if (apiKeysWithoutPlaintext.length === 0) {
      console.log('No migration needed - all API keys have plaintext keys');
      return;
    }
    
    // For existing keys without plaintext, we'll set a placeholder that indicates they need regeneration
    const REGENERATION_PLACEHOLDER = 'REGENERATION_REQUIRED_AFTER_MIGRATION';
    
    for (const apiKey of apiKeysWithoutPlaintext) {
      await prisma.licenseApiKey.update({
        where: { id: apiKey.id },
        data: {
          plaintextKey: REGENERATION_PLACEHOLDER,
          isActive: false // Deactivate until regenerated
        }
      });
      
      console.log(`Updated API key ${apiKey.id} for server ${apiKey.serverIp} (License: ${apiKey.license.licenseType})`);
    }
    
    console.log('\nMigration completed successfully!');
    console.log('\nIMPORTANT: Users will need to regenerate their API keys as the original plaintext versions');
    console.log('cannot be recovered from the hashed versions stored in the database.');
    console.log('\nAffected users should:');
    console.log('1. Go to the license management page');
    console.log('2. Delete the old API keys');
    console.log('3. Create new API keys for their servers');
    
  } catch (error) {
    console.error('Migration failed:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run the migration
if (require.main === module) {
  migrateApiKeys()
    .then(() => {
      console.log('Migration script completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('Migration script failed:', error);
      process.exit(1);
    });
}

module.exports = { migrateApiKeys };
